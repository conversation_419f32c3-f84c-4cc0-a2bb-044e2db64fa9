<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Building;
use App\Models\Expense;
use App\Models\Income;
use App\Models\Payment;
use App\Models\ExpenseType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;

class SecurityTest extends TestCase
{
    use RefreshDatabase;

    protected $building1;
    protected $building2;
    protected $admin1;
    protected $admin2;
    protected $neighbor1;
    protected $neighbor2;
    protected $superAdmin;

    protected function setUp(): void
    {
        parent::setUp();

        // Create buildings
        $this->building1 = Building::factory()->create(['name' => 'Building 1']);
        $this->building2 = Building::factory()->create(['name' => 'Building 2']);

        // Create users
        $this->admin1 = User::factory()->create([
            'role' => 'admin',
            'building_id' => $this->building1->id
        ]);
        
        $this->admin2 = User::factory()->create([
            'role' => 'admin',
            'building_id' => $this->building2->id
        ]);

        $this->neighbor1 = User::factory()->create([
            'role' => 'neighbor',
            'building_id' => $this->building1->id
        ]);

        $this->neighbor2 = User::factory()->create([
            'role' => 'neighbor',
            'building_id' => $this->building2->id
        ]);

        $this->superAdmin = User::factory()->create([
            'role' => 'super_admin',
            'building_id' => null
        ]);
    }

    /** @test */
    public function admin_cannot_access_expenses_from_other_buildings()
    {
        Sanctum::actingAs($this->admin1);

        // Create expense in building 2
        $expense = Expense::factory()->create([
            'building_id' => $this->building2->id,
            'user_id' => $this->neighbor2->id
        ]);

        // Admin 1 should not be able to view expense from building 2
        $response = $this->getJson("/api/expenses/{$expense->id}");
        $response->assertStatus(403);
        $response->assertJson(['message' => 'Unauthorized. You can only view expenses in your building.']);
    }

    /** @test */
    public function admin_cannot_update_expenses_from_other_buildings()
    {
        Sanctum::actingAs($this->admin1);

        $expenseType = ExpenseType::factory()->create();
        
        // Create expense in building 2
        $expense = Expense::factory()->create([
            'building_id' => $this->building2->id,
            'user_id' => $this->neighbor2->id
        ]);

        // Admin 1 should not be able to update expense from building 2
        $response = $this->putJson("/api/expenses/{$expense->id}", [
            'expense_type_id' => $expenseType->id,
            'user_id' => $this->neighbor2->id,
            'amount' => 100,
            'month' => '01',
            'year' => 2024,
            'notes' => 'Test'
        ]);
        
        $response->assertStatus(403);
        $response->assertJson(['message' => 'Unauthorized. You can only update expenses in your building.']);
    }

    /** @test */
    public function admin_cannot_delete_expenses_from_other_buildings()
    {
        Sanctum::actingAs($this->admin1);

        // Create expense in building 2
        $expense = Expense::factory()->create([
            'building_id' => $this->building2->id,
            'user_id' => $this->neighbor2->id
        ]);

        // Admin 1 should not be able to delete expense from building 2
        $response = $this->deleteJson("/api/expenses/{$expense->id}");
        $response->assertStatus(403);
        $response->assertJson(['message' => 'Unauthorized. You can only delete expenses in your building.']);
    }

    /** @test */
    public function admin_cannot_access_incomes_from_other_buildings()
    {
        Sanctum::actingAs($this->admin1);

        // Create income in building 2
        $income = Income::factory()->create([
            'building_id' => $this->building2->id,
            'user_id' => $this->neighbor2->id
        ]);

        // Admin 1 should not be able to view income from building 2
        $response = $this->getJson("/api/incomes/{$income->id}");
        $response->assertStatus(403);
        $response->assertJson(['message' => 'Unauthorized. You can only view income records in your building.']);
    }

    /** @test */
    public function neighbor_cannot_access_admin_routes()
    {
        Sanctum::actingAs($this->neighbor1);

        // Neighbor should not be able to access admin routes
        $response = $this->getJson('/api/expenses');
        $response->assertStatus(200); // This should work for reading

        // But creating should fail
        $response = $this->postJson('/api/expenses', []);
        $response->assertStatus(403);
    }

    /** @test */
    public function super_admin_can_access_all_buildings()
    {
        Sanctum::actingAs($this->superAdmin);

        // Create expenses in both buildings
        $expense1 = Expense::factory()->create([
            'building_id' => $this->building1->id,
            'user_id' => $this->neighbor1->id
        ]);
        
        $expense2 = Expense::factory()->create([
            'building_id' => $this->building2->id,
            'user_id' => $this->neighbor2->id
        ]);

        // Super admin should be able to access both
        $response = $this->getJson("/api/expenses/{$expense1->id}");
        $response->assertStatus(200);

        $response = $this->getJson("/api/expenses/{$expense2->id}");
        $response->assertStatus(200);
    }
}
