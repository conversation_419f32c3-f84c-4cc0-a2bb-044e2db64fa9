<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\AuthController;
use App\Http\Controllers\API\ExpenseTypeController;
use App\Http\Controllers\API\ExpenseController;
use App\Http\Controllers\API\IncomeController;
use App\Http\Controllers\API\PaymentController;
use App\Http\Controllers\API\BuildingController;
use App\Http\Controllers\Admin\UserController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

// Public routes
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    // Auth routes
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/user', [AuthController::class, 'user']);
    Route::put('/profile', [AuthController::class, 'updateProfile']);

    // Expense Types Routes (Admin only)
    Route::apiResource('expense-types', ExpenseTypeController::class)->middleware('admin');

    // Expenses Routes (Admin only for CUD operations, authenticated for read)
    Route::get('expenses', [ExpenseController::class, 'index']);
    Route::get('expenses/{expense}', [ExpenseController::class, 'show']);
    Route::get('expenses/monthly', [ExpenseController::class, 'getMonthlyExpenses']);
    Route::get('expenses/summary', [ExpenseController::class, 'getExpenseSummary']);
    Route::middleware('admin')->group(function () {
        Route::post('expenses', [ExpenseController::class, 'store']);
        Route::put('expenses/{expense}', [ExpenseController::class, 'update']);
        Route::delete('expenses/{expense}', [ExpenseController::class, 'destroy']);
        Route::post('expenses/generate-monthly', [ExpenseController::class, 'generateMonthlyExpenses']);
    });

    // Incomes Routes (Admin only for CUD operations, authenticated for read)
    Route::get('incomes', [IncomeController::class, 'index']);
    Route::get('incomes/{income}', [IncomeController::class, 'show']);
    Route::get('incomes/summary', [IncomeController::class, 'getSummary']);
    Route::middleware('admin')->group(function () {
        Route::post('incomes', [IncomeController::class, 'store']);
        Route::put('incomes/{income}', [IncomeController::class, 'update']);
        Route::delete('incomes/{income}', [IncomeController::class, 'destroy']);
    });

    // Buildings Routes
    Route::apiResource('buildings', BuildingController::class)->middleware('super_admin');

    // Admin building management
    Route::middleware('admin')->group(function () {
        Route::get('/my-building', [BuildingController::class, 'getMyBuilding']);
        Route::put('/my-building', [BuildingController::class, 'updateMyBuilding']);
    });

    // Payments Routes (Admin only for CUD operations, authenticated for read)
    Route::get('payments', [PaymentController::class, 'index']);
    Route::get('payments/{payment}', [PaymentController::class, 'show']);
    Route::get('payments/user', [PaymentController::class, 'getUserPayments']);
    Route::get('payments/expense', [PaymentController::class, 'getExpensePayments']);
    Route::get('payments/summary', [PaymentController::class, 'getPaymentSummary']);
    Route::middleware('admin')->group(function () {
        Route::post('payments', [PaymentController::class, 'store']);
        Route::put('payments/{payment}', [PaymentController::class, 'update']);
        Route::delete('payments/{payment}', [PaymentController::class, 'destroy']);
    });

    // Admin routes
    Route::prefix('admin')->middleware('admin')->group(function () {
        Route::get('/users', [UserController::class, 'index']);
    });

    // Admin routes for user management (with building scope check in controller)
    Route::prefix('admin')->middleware('admin')->group(function () {
        Route::post('/users', [UserController::class, 'store']);
        Route::put('/users/{user}', [UserController::class, 'update']);
        Route::delete('/users/{user}', [UserController::class, 'destroy']);
    });
    
});
