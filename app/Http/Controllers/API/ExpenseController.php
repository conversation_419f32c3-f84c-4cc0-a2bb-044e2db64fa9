<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Expense;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ExpenseController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();
        $query = Expense::with(['expenseType', 'user', 'payments']);

        // Filter by user's building (unless super admin)
        if ($user->role !== 'super_admin') {
            $query->where('building_id', $user->building_id);
        }

        // Apply filters
        if ($request->filled('type')) {
            $query->whereHas('expenseType', function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->type . '%');
            });
        }

        if ($request->filled('month')) {
            $query->where('month', $request->month);
        }

        if ($request->filled('year')) {
            $query->where('year', $request->year);
        }



        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->filled('is_automatic')) {
            $query->where('is_automatic', $request->is_automatic);
        }

        // Order by latest
        $query->latest();

        // Paginate results
        $expenses = $query->paginate(50);

        return response()->json($expenses);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $user = $request->user();

        $validated = $request->validate([
            'expense_type_id' => 'required|exists:expense_types,id',
            'user_id' => 'required|exists:users,id',
            'amount' => 'required|numeric|min:0',
            'month' => 'required|string|size:2',
            'year' => 'required|integer|min:2020|max:2030',
            'notes' => 'nullable|string',
            'is_automatic' => 'boolean',
        ]);

        // Auto-set building_id from logged-in user
        $validated['building_id'] = $user->building_id;

        // Validate that the selected user belongs to the same building (unless super admin)
        if ($user->role !== 'super_admin') {
            $selectedUser = \App\Models\User::find($validated['user_id']);
            if (!$selectedUser || $selectedUser->building_id !== $user->building_id) {
                return response()->json(['message' => 'You can only create expenses for users in your building.'], 403);
            }
        }

        // Convert year to string for storage
        $validated['year'] = (string) $validated['year'];



        // Auto-generate due_date as end of month
        $validated['due_date'] = \Carbon\Carbon::createFromDate($validated['year'], $validated['month'], 1)->endOfMonth();

        $expense = Expense::create($validated);
        $expense->load(['expenseType', 'user']);
        return response()->json($expense, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request, Expense $expense): JsonResponse
    {
        $user = $request->user();

        // Check building scope authorization (unless super admin)
        if ($user->role !== 'super_admin') {
            if ($expense->building_id !== $user->building_id) {
                return response()->json(['message' => 'Unauthorized. You can only view expenses in your building.'], 403);
            }
        }

        $expense->load(['expenseType', 'user', 'payments']);
        return response()->json($expense);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Expense $expense): JsonResponse
    {
        $user = $request->user();

        // Check building scope authorization (unless super admin)
        if ($user->role !== 'super_admin') {
            if ($expense->building_id !== $user->building_id) {
                return response()->json(['message' => 'Unauthorized. You can only update expenses in your building.'], 403);
            }
        }

        $validated = $request->validate([
            'expense_type_id' => 'required|exists:expense_types,id',
            'user_id' => 'required|exists:users,id',
            'amount' => 'required|numeric|min:0',
            'month' => 'required|string|size:2',
            'year' => 'required|integer|min:2020|max:2030',
            'notes' => 'nullable|string',
            'is_automatic' => 'boolean',
        ]);

        // Validate that the selected user belongs to the same building (unless super admin)
        if ($user->role !== 'super_admin') {
            $selectedUser = \App\Models\User::find($validated['user_id']);
            if (!$selectedUser || $selectedUser->building_id !== $user->building_id) {
                return response()->json(['message' => 'You can only assign expenses to users in your building.'], 403);
            }
        }

        // Convert year to string for storage
        $validated['year'] = (string) $validated['year'];

        // Keep the original building_id (don't allow changing it unless super admin)
        if ($user->role !== 'super_admin') {
            $validated['building_id'] = $expense->building_id;
        }

        // Auto-generate due_date as end of month if not provided
        if (!isset($validated['due_date'])) {
            $validated['due_date'] = \Carbon\Carbon::createFromDate($validated['year'], $validated['month'], 1)->endOfMonth();
        }

        $expense->update($validated);
        $expense->load(['expenseType', 'user']);
        return response()->json($expense);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, Expense $expense): JsonResponse
    {
        $user = $request->user();

        // Check building scope authorization (unless super admin)
        if ($user->role !== 'super_admin') {
            if ($expense->building_id !== $user->building_id) {
                return response()->json(['message' => 'Unauthorized. You can only delete expenses in your building.'], 403);
            }
        }

        $expense->delete();
        return response()->json(null, 204);
    }

    public function getMonthlyExpenses(Request $request): JsonResponse
    {
        $user = $request->user();

        $validated = $request->validate([
            'month' => 'required|string',
            'year' => 'required|string',
        ]);

        $query = Expense::with(['expenseType', 'payments'])
            ->where('month', $validated['month'])
            ->where('year', $validated['year']);

        // Filter by user's building (unless super admin)
        if ($user->role !== 'super_admin') {
            $query->where('building_id', $user->building_id);
        }

        $expenses = $query->get();
        return response()->json($expenses);
    }

    public function getExpenseSummary(Request $request): JsonResponse
    {
        $user = $request->user();
        $query = Expense::selectRaw('
            expense_type_id,
            month,
            year,
            SUM(amount) as total_amount,
            COUNT(*) as total_expenses,
            COUNT(*) as total_count
        ');

        // Filter by user's building (unless super admin)
        if ($user->role !== 'super_admin') {
            $query->where('building_id', $user->building_id);
        }

        $summary = $query->groupBy('expense_type_id', 'month', 'year')
            ->with('expenseType')
            ->get();

        return response()->json($summary);
    }

    public function generateMonthlyExpenses(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'month' => 'required|string|size:2',
            'year' => 'required|string|size:4',
        ]);

        $user = $request->user();
        $commandParams = [
            '--month' => $validated['month'],
            '--year' => $validated['year'],
        ];

        // If user is admin (not super_admin), only generate for their building
        if ($user->role === 'admin' && $user->building_id) {
            $commandParams['--building'] = $user->building_id;
        }

        // Call the artisan command
        \Artisan::call('expenses:generate-monthly', $commandParams);

        $output = \Artisan::output();

        return response()->json([
            'message' => 'Monthly expenses generation completed',
            'output' => $output
        ]);
    }
}
