<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Building;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class BuildingController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): JsonResponse
    {
        $buildings = Building::all();
        return response()->json($buildings);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'address' => 'nullable|string',
            'city' => 'nullable|string',
            'country' => 'nullable|string',
            'postal_code' => 'nullable|string',
            'description' => 'nullable|string',
            'monthly_fee' => 'required|numeric|min:0',
        ]);

        $building = Building::create($validated);
        return response()->json($building, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Building $building): JsonResponse
    {
        return response()->json($building);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Building $building): JsonResponse
    {
        $user = $request->user();

        // Check if user is admin and can only update their own building
        if ($user->role === 'admin' && $user->building_id !== $building->id) {
            return response()->json(['message' => 'Unauthorized. You can only update your own building.'], 403);
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'address' => 'nullable|string',
            'city' => 'nullable|string',
            'country' => 'nullable|string',
            'postal_code' => 'nullable|string',
            'description' => 'nullable|string',
            'monthly_fee' => 'required|numeric|min:0',
        ]);

        $building->update($validated);
        return response()->json($building);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Building $building): JsonResponse
    {
        $building->delete();
        return response()->json(null, 204);
    }

    /**
     * Get the current admin's building
     */
    public function getMyBuilding(Request $request): JsonResponse
    {
        $user = $request->user();

        if (!$user->building_id) {
            return response()->json(['message' => 'No building assigned to this user.'], 404);
        }

        $building = Building::find($user->building_id);

        if (!$building) {
            return response()->json(['message' => 'Building not found.'], 404);
        }

        return response()->json($building);
    }

    /**
     * Update the current admin's building
     */
    public function updateMyBuilding(Request $request): JsonResponse
    {
        $user = $request->user();

        if (!$user->building_id) {
            return response()->json(['message' => 'No building assigned to this user.'], 404);
        }

        $building = Building::find($user->building_id);

        if (!$building) {
            return response()->json(['message' => 'Building not found.'], 404);
        }

        // Use the existing update logic with building ownership check
        return $this->update($request, $building);
    }
}
