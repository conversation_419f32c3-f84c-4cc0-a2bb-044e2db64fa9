<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class PaymentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();
        $query = Payment::with(['user', 'expense']);

        // Filter by user's building (unless super admin)
        if ($user->role !== 'super_admin') {
            $query->whereHas('expense', function ($q) use ($user) {
                $q->where('building_id', $user->building_id);
            });
        }

        $payments = $query->get();
        return response()->json($payments);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $user = $request->user();

        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'expense_id' => 'required|exists:expenses,id',
            'amount' => 'required|numeric|min:0',
            'payment_date' => 'required|date',
            'payment_method' => 'required|in:cash,bank_transfer,check',
            'reference_number' => 'nullable|string',
            'notes' => 'nullable|string',
            'status' => 'required|in:pending,completed,failed',
        ]);

        // Validate building scope authorization (unless super admin)
        if ($user->role !== 'super_admin') {
            // Check if the expense belongs to the user's building
            $expense = \App\Models\Expense::find($validated['expense_id']);
            if (!$expense || $expense->building_id !== $user->building_id) {
                return response()->json(['message' => 'You can only create payments for expenses in your building.'], 403);
            }

            // Check if the user belongs to the same building
            $selectedUser = \App\Models\User::find($validated['user_id']);
            if (!$selectedUser || $selectedUser->building_id !== $user->building_id) {
                return response()->json(['message' => 'You can only create payments for users in your building.'], 403);
            }
        }

        $payment = Payment::create($validated);
        return response()->json($payment, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request, Payment $payment): JsonResponse
    {
        $user = $request->user();

        // Check building scope authorization (unless super admin)
        if ($user->role !== 'super_admin') {
            $payment->load(['expense']);
            if (!$payment->expense || $payment->expense->building_id !== $user->building_id) {
                return response()->json(['message' => 'Unauthorized. You can only view payments in your building.'], 403);
            }
        }

        $payment->load(['user', 'expense']);
        return response()->json($payment);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Payment $payment): JsonResponse
    {
        $user = $request->user();

        // Check building scope authorization (unless super admin)
        if ($user->role !== 'super_admin') {
            $payment->load(['expense']);
            if (!$payment->expense || $payment->expense->building_id !== $user->building_id) {
                return response()->json(['message' => 'Unauthorized. You can only update payments in your building.'], 403);
            }
        }

        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'expense_id' => 'required|exists:expenses,id',
            'amount' => 'required|numeric|min:0',
            'payment_date' => 'required|date',
            'payment_method' => 'required|in:cash,bank_transfer,check',
            'reference_number' => 'nullable|string',
            'notes' => 'nullable|string',
            'status' => 'required|in:pending,completed,failed',
        ]);

        // Validate building scope for new relationships (unless super admin)
        if ($user->role !== 'super_admin') {
            // Check if the new expense belongs to the user's building
            $expense = \App\Models\Expense::find($validated['expense_id']);
            if (!$expense || $expense->building_id !== $user->building_id) {
                return response()->json(['message' => 'You can only assign payments to expenses in your building.'], 403);
            }

            // Check if the new user belongs to the same building
            $selectedUser = \App\Models\User::find($validated['user_id']);
            if (!$selectedUser || $selectedUser->building_id !== $user->building_id) {
                return response()->json(['message' => 'You can only assign payments to users in your building.'], 403);
            }
        }

        $payment->update($validated);
        return response()->json($payment);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, Payment $payment): JsonResponse
    {
        $user = $request->user();

        // Check building scope authorization (unless super admin)
        if ($user->role !== 'super_admin') {
            $payment->load(['expense']);
            if (!$payment->expense || $payment->expense->building_id !== $user->building_id) {
                return response()->json(['message' => 'Unauthorized. You can only delete payments in your building.'], 403);
            }
        }

        $payment->delete();
        return response()->json(null, 204);
    }

    public function getUserPayments(Request $request): JsonResponse
    {
        $user = $request->user();

        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
        ]);

        // Validate building scope authorization (unless super admin)
        if ($user->role !== 'super_admin') {
            $selectedUser = \App\Models\User::find($validated['user_id']);
            if (!$selectedUser || $selectedUser->building_id !== $user->building_id) {
                return response()->json(['message' => 'You can only view payments for users in your building.'], 403);
            }
        }

        $query = Payment::with(['expense'])
            ->where('user_id', $validated['user_id']);

        // Additional building scope filter (unless super admin)
        if ($user->role !== 'super_admin') {
            $query->whereHas('expense', function ($q) use ($user) {
                $q->where('building_id', $user->building_id);
            });
        }

        $payments = $query->get();
        return response()->json($payments);
    }

    public function getExpensePayments(Request $request): JsonResponse
    {
        $user = $request->user();

        $validated = $request->validate([
            'expense_id' => 'required|exists:expenses,id',
        ]);

        // Validate building scope authorization (unless super admin)
        if ($user->role !== 'super_admin') {
            $expense = \App\Models\Expense::find($validated['expense_id']);
            if (!$expense || $expense->building_id !== $user->building_id) {
                return response()->json(['message' => 'You can only view payments for expenses in your building.'], 403);
            }
        }

        $payments = Payment::with(['user'])
            ->where('expense_id', $validated['expense_id'])
            ->get();

        return response()->json($payments);
    }

    public function getPaymentSummary(Request $request): JsonResponse
    {
        $user = $request->user();
        $query = Payment::selectRaw('
            payment_method,
            status,
            COUNT(*) as total_payments,
            SUM(amount) as total_amount
        ');

        // Filter by user's building (unless super admin)
        if ($user->role !== 'super_admin') {
            $query->whereHas('expense', function ($q) use ($user) {
                $q->where('building_id', $user->building_id);
            });
        }

        $summary = $query->groupBy('payment_method', 'status')->get();
        return response()->json($summary);
    }
}
