<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;

class UserController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();

        if ($user->role === 'super_admin') {
            // Super admin sees all users with building relationship
            $users = User::with('building')->latest()->paginate(50);
        } else {
            // Regular admin sees only users in their building
            $users = User::with('building')
                ->where('building_id', $user->building_id)
                ->latest()
                ->paginate(50);
        }

        // Return JSON for API requests
        if ($request->expectsJson()) {
            return response()->json($users);
        }

        // Return view for web requests
        return view('admin.users.index', compact('users'));
    }

    public function create()
    {
        return view('admin.users.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'confirmed', Password::defaults()],
            'apartment_number' => ['required', 'string', 'max:10'],
            'role' => ['required', 'string', 'in:super_admin,admin,neighbor'],
            'building_id' => ['nullable', 'exists:buildings,id'],
        ]);

        // Check if the requesting user is admin (not super_admin) and enforce building_id match
        $requestingUser = $request->user();
        if ($requestingUser->role !== 'super_admin') {
            if ($validated['role'] === 'super_admin') {
                return response()->json(['message' => 'Unauthorized. Only Super Admin can create Super Admin users.'], 403);
            }
            // Auto-set building_id for non-super admins
            $validated['building_id'] = $requestingUser->building_id;
        }

        $user = User::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => Hash::make($validated['password']),
            'apartment_number' => $validated['apartment_number'],
            'role' => $validated['role'],
            'building_id' => $validated['role'] === 'super_admin' ? null : ($validated['building_id'] ?? null),
        ]);

        // Return JSON for API requests
        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'User created successfully.',
                'user' => $user
            ], 201);
        }

        // Return redirect for web requests
        return redirect()->route('admin.users.index')
            ->with('success', 'User created successfully.');
    }

    public function edit(User $user)
    {
        return view('admin.users.edit', compact('user'));
    }

    public function update(Request $request, User $user)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email,' . $user->id],
            'password' => ['nullable', 'confirmed', Password::defaults()],
            'apartment_number' => ['required', 'string', 'max:10'],
            'role' => ['required', 'string', 'in:super_admin,admin,neighbor'],
            'building_id' => ['nullable', 'exists:buildings,id'],
        ]);

        // Check if the requesting user is admin (not super_admin) and enforce building_id match
        $requestingUser = $request->user();
        if ($requestingUser->role !== 'super_admin') {
            if ($validated['role'] === 'super_admin' || $user->role === 'super_admin') {
                return response()->json(['message' => 'Unauthorized. Only Super Admin can modify Super Admin users.'], 403);
            }
            if ($user->building_id !== $requestingUser->building_id) {
                return response()->json(['message' => 'Unauthorized. You can only update users in your building.'], 403);
            }
            // Auto-set building_id for non-super admins
            $validated['building_id'] = $requestingUser->building_id;
        }

        $user->name = $validated['name'];
        $user->email = $validated['email'];
        $user->apartment_number = $validated['apartment_number'];
        $user->role = $validated['role'];
        $user->building_id = $validated['role'] === 'super_admin' ? null : ($validated['building_id'] ?? $user->building_id);

        if ($request->filled('password')) {
            $user->password = Hash::make($validated['password']);
        }

        $user->save();

        // Return JSON for API requests
        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'User updated successfully.',
                'user' => $user
            ]);
        }

        // Return redirect for web requests
        return redirect()->route('admin.users.index')
            ->with('success', 'User updated successfully.');
    }

    public function destroy(Request $request, User $user)
    {
        // Check if the requesting user is admin (not super_admin) and enforce building_id match
        $requestingUser = $request->user();
        if ($requestingUser->role !== 'super_admin') {
            if ($user->role === 'super_admin') {
                return response()->json(['message' => 'Unauthorized. Only Super Admin can delete Super Admin users.'], 403);
            }
            if ($user->building_id !== $requestingUser->building_id) {
                return response()->json(['message' => 'Unauthorized. You can only delete users in your building.'], 403);
            }
        }

        $user->delete();

        // Return JSON for API requests
        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'User deleted successfully.'
            ]);
        }

        // Return redirect for web requests
        return redirect()->route('admin.users.index')
            ->with('success', 'User deleted successfully.');
    }
}
