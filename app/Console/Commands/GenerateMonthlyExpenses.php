<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\Expense;
use App\Models\ExpenseType;
use App\Models\Building;
use Illuminate\Console\Command;
use Carbon\Carbon;

class GenerateMonthlyExpenses extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'expenses:generate-monthly {--month=} {--year=} {--building=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate monthly expenses for neighbors based on building monthly fee';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $month = $this->option('month') ?: Carbon::now()->format('m');
        $year = $this->option('year') ?: Carbon::now()->format('Y');
        $buildingId = $this->option('building');

        // Get or create the monthly maintenance expense type
        $expenseType = ExpenseType::firstOrCreate(
            ['name' => 'Monthly Maintenance'],
            ['description' => 'Monthly maintenance fee for all neighbors']
        );

        // Get neighbors based on building filter
        $neighborsQuery = User::where('role', 'neighbor');

        if ($buildingId) {
            $neighborsQuery->where('building_id', $buildingId);
        }

        $neighbors = $neighborsQuery->with('building')->get();

        $created = 0;
        $skipped = 0;
        $buildingStats = [];

        foreach ($neighbors as $neighbor) {
            // Skip if neighbor has no building assigned
            if (!$neighbor->building) {
                $this->warn("Skipping neighbor {$neighbor->name} - no building assigned");
                continue;
            }

            // Check if expense already exists for this neighbor, month, and year
            $existingExpense = Expense::where('user_id', $neighbor->id)
                ->where('expense_type_id', $expenseType->id)
                ->where('month', $month)
                ->where('year', $year)
                ->where('is_automatic', true)
                ->first();

            if ($existingExpense) {
                $skipped++;
                continue;
            }

            // Use building's monthly fee
            $monthlyFee = $neighbor->building->monthly_fee ?? 70.00;

            // Create the monthly expense
            Expense::create([
                'expense_type_id' => $expenseType->id,
                'user_id' => $neighbor->id,
                'building_id' => $neighbor->building_id,
                'amount' => $monthlyFee,
                'due_date' => Carbon::createFromDate($year, $month, 1)->endOfMonth(),
                'month' => $month,
                'year' => $year,
                'notes' => 'Monthly maintenance fee',
                'status' => 'pending',
                'is_automatic' => true,
            ]);

            $created++;

            // Track stats per building
            $buildingName = $neighbor->building->name;
            if (!isset($buildingStats[$buildingName])) {
                $buildingStats[$buildingName] = ['count' => 0, 'fee' => $monthlyFee];
            }
            $buildingStats[$buildingName]['count']++;
        }

        $this->info("Monthly expenses generated for {$month}/{$year}:");
        $this->info("Created: {$created} expenses");
        $this->info("Skipped: {$skipped} expenses (already exist)");

        if (!empty($buildingStats)) {
            $this->info("\nBreakdown by building:");
            foreach ($buildingStats as $buildingName => $stats) {
                $this->info("- {$buildingName}: {$stats['count']} neighbors × ₪{$stats['fee']}");
            }
        }

        return Command::SUCCESS;
    }
}
