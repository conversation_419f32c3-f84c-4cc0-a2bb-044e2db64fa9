import i18n from '../i18n/index.js';

export default {
  install(app) {
    // Make i18n available globally
    app.config.globalProperties.$i18n = i18n;
    app.config.globalProperties.$t = (key, params) => i18n.t(key, params);
    app.config.globalProperties.$locale = () => i18n.getLocale();
    app.config.globalProperties.$isRTL = () => i18n.isRTL();
    app.config.globalProperties.$setLocale = (locale) => i18n.setLocale(locale);
    
    // Provide for composition API
    app.provide('i18n', i18n);
  }
};
