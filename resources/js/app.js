import './bootstrap';
import { createApp } from 'vue';
import App from './components/App.vue';
import router from './router';
import axios from 'axios';
import i18nPlugin from './plugins/i18n.js';

// Configure Axios
// Base URL is already set in bootstrap.js
axios.interceptors.request.use(config => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle 401 Unauthorized responses
axios.interceptors.response.use(
  response => response,
  error => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      router.push('/login');
    }
    return Promise.reject(error);
  }
);

const app = createApp(App);
app.config.globalProperties.$axios = axios;
app.use(router);
app.use(i18nPlugin);
app.mount('#app');
