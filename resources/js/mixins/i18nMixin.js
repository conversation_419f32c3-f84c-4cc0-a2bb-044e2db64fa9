// Mixin to make components reactive to language changes
export default {
  data() {
    return {
      // This reactive property will force re-render when language changes
      localeUpdateKey: 0
    };
  },
  
  mounted() {
    // Listen for locale changes and force component update
    window.addEventListener('localeChanged', this.handleLocaleChange);
    window.addEventListener('forceUpdate', this.handleForceUpdate);
  },
  
  beforeUnmount() {
    // Clean up event listeners
    window.removeEventListener('localeChanged', this.handleLocaleChange);
    window.removeEventListener('forceUpdate', this.handleForceUpdate);
  },
  
  methods: {
    handleLocaleChange() {
      // Force component re-render by updating reactive key
      this.localeUpdateKey++;
      this.$forceUpdate();
      
      // Re-initialize columns if the component has this method
      if (typeof this.initializeColumns === 'function') {
        this.initializeColumns();
      }
    },
    
    handleForceUpdate() {
      // Force component re-render
      this.localeUpdateKey++;
      this.$forceUpdate();
    }
  }
};
