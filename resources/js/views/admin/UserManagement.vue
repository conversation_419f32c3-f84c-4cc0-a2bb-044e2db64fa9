<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Page Content -->
    <div class="max-w-6xl mx-auto px-8 py-8">

      <div v-if="loading" class="text-center py-4">
        <p>Loading...</p>
      </div>

      <div v-else-if="isAdmin">
        <!-- Filters Section -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Role</label>
            <select v-model="filters.role" class="w-full px-3 py-2 border border-gray-300 rounded-md">
              <option value="">All Roles</option>
              <option v-if="isSuperAdmin" value="admin">Admin</option>
              <option v-if="!isSuperAdmin" value="neighbor">Neighbor</option>
            </select>
          </div>

          <div v-if="isSuperAdmin">
            <label class="block text-sm font-medium text-gray-700 mb-2">Building</label>
            <select v-model="filters.building_id" class="w-full px-3 py-2 border border-gray-300 rounded-md">
              <option value="">All Buildings</option>
              <option v-for="building in buildings" :key="building.id" :value="building.id">
                {{ building.name }}
              </option>
            </select>
          </div>

          <div class="flex items-end">
            <button
              @click="applyFilters"
              class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Apply Filters
            </button>
          </div>
        </div>

        <div class="mt-4 flex justify-between items-center">
          <router-link
            to="/admin/users/create"
            class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
          >
            Add New User
          </router-link>
        </div>
      </div>

      <!-- Summary Cards -->
      <div v-if="isAdmin" class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div class="bg-white p-6 rounded-lg shadow-sm">
          <h3 class="text-lg font-semibold text-gray-700">Total Users</h3>
          <p class="text-3xl font-bold text-blue-600">{{ totalUsers }}</p>
        </div>
        <div class="bg-white p-6 rounded-lg shadow-sm">
          <h3 class="text-lg font-semibold text-gray-700">{{ isSuperAdmin ? 'Admins' : 'Neighbors' }}</h3>
          <p class="text-3xl font-bold text-green-600">{{ roleBasedCount }}</p>
        </div>
        <div class="bg-white p-6 rounded-lg shadow-sm">
          <h3 class="text-lg font-semibold text-gray-700">{{ isSuperAdmin ? 'Buildings' : 'Active Users' }}</h3>
          <p class="text-3xl font-bold text-purple-600">{{ thirdMetric }}</p>
        </div>
      </div>

      <!-- Users Table -->
      <data-table
        v-if="isAdmin"
        title="Users"
        :columns="columns"
        :items="filteredUsers"
        :loading="loading"
      >
        <template #actions="{ item }">
          <div class="flex space-x-2">
            <button
              @click="editUser(item)"
              class="text-indigo-600 hover:text-indigo-900"
            >
              Edit
            </button>
            <button
              @click="deleteUser(item)"
              class="text-red-600 hover:text-red-900"
            >
              Delete
            </button>
          </div>
        </template>
      </data-table>

      <!-- Edit Modal -->
      <div v-if="showEditModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4">
          <div class="flex justify-between items-center p-6 border-b">
            <h2 class="text-xl font-semibold text-gray-900">Edit User</h2>
            <button @click="closeEditModal" class="text-gray-400 hover:text-gray-600">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>

          <!-- Modal body -->
          <div class="p-6">
            <user-form
              :user="selectedUser"
              :is-edit="true"
              :is-super-admin="isSuperAdmin"
              :admin-building-id="adminBuildingId"
              :buildings="buildings"
              @success="handleEditSuccess"
              @error="handleEditError"
              @cancel="closeEditModal"
            />
          </div>
        </div>
      </div>

      <!-- Notifications -->
      <notification
        :show="showNotification"
        :type="notificationType"
        :title="notificationTitle"
        :message="notificationMessage"
        @close="closeNotification"
      />
      <div v-if="!isAdmin" class="text-center py-8">
        <p class="text-red-500">You do not have permission to access this page.</p>
      </div>
    </div>
  </div>
</template>

<script>
import DataTable from '../../components/DataTable.vue';
import UserForm from '../../components/UserForm.vue';
import Notification from '../../components/Notification.vue';
import i18nMixin from '../../mixins/i18nMixin.js';

export default {
  mixins: [i18nMixin],
  components: {
    DataTable,
    UserForm,
    Notification
  },
  name: 'UserManagement',
  data() {
    return {
      loading: false,
      users: [],
      buildings: [],
      selectedUser: null,
      showEditModal: false,
      showNotification: false,
      notificationType: 'info',
      notificationTitle: '',
      notificationMessage: '',
      filters: {
        role: '',
        building_id: ''
      },
      user: null,
      isAdmin: false,
      isSuperAdmin: false,
      adminBuildingId: null,
      userLoaded: false,
      columns: []
    };
  },
  computed: {
    filteredUsers() {
      let filtered = this.users;

      if (this.filters.role) {
        filtered = filtered.filter(user => user.role === this.filters.role);
      }

      if (this.filters.building_id) {
        filtered = filtered.filter(user => user.building_id == this.filters.building_id);
      }

      return filtered;
    },
    totalUsers() {
      return this.users.length;
    },
    roleBasedCount() {
      if (this.isSuperAdmin) {
        return this.users.filter(user => user.role === 'admin').length;
      } else {
        return this.users.filter(user => user.role === 'neighbor').length;
      }
    },
    thirdMetric() {
      if (this.isSuperAdmin) {
        return this.buildings.length;
      } else {
        return this.users.filter(user => user.role !== 'super_admin').length;
      }
    }
  },
  async mounted() {
    // First try to get user data from localStorage for immediate role determination
    this.initializeUserFromStorage();
    await this.fetchUser();
    if (this.isAdmin) {
      this.setupColumns();
      this.loadUsers();
      this.fetchBuildings();
    }
  },
  methods: {
    initializeUserFromStorage() {
      try {
        const userData = JSON.parse(localStorage.getItem('user') || 'null');
        if (userData && userData.role) {
          this.isAdmin = userData.role === 'admin' || userData.role === 'super_admin';
          this.isSuperAdmin = userData.role === 'super_admin';
          this.adminBuildingId = userData.building_id;
          this.userLoaded = true;
        }
      } catch (error) {
        console.error('Error parsing user data from localStorage:', error);
      }
    },
    async fetchUser() {
      try {
        const response = await this.$axios.get('/user');
        this.user = response.data;
        this.isAdmin = this.user.role === 'admin' || this.user.role === 'super_admin';
        this.isSuperAdmin = this.user.role === 'super_admin';
        this.adminBuildingId = this.user.building_id;
        this.userLoaded = true;
        // Update localStorage with fresh user data
        localStorage.setItem('user', JSON.stringify(this.user));
      } catch (error) {
        console.error('Error fetching user:', error);
        this.$router.push('/login');
      } finally {
        this.loading = false;
      }
    },
    setupColumns() {
      if (this.isSuperAdmin) {
        this.columns = [
          { key: 'name', label: 'Name' },
          { key: 'email', label: 'Email' },
          { key: 'role', label: 'Role' },
          { key: 'building.name', label: 'Building' }
        ];
      } else {
        this.columns = [
          { key: 'name', label: 'Name' },
          { key: 'email', label: 'Email' },
          { key: 'apartment_number', label: 'Apartment' },
          { key: 'role', label: 'Role' }
        ];
      }
    },
    async loadUsers() {
      this.loading = true;
      try {
        const response = await this.$axios.get('/admin/users');
        let usersData = response.data.data || response.data;

        if (this.isSuperAdmin) {
          // Super admin sees only admins and buildings
          this.users = usersData.filter(user => user.role === 'admin');
        } else {
          // Regular admin sees only neighbors in their building
          this.users = usersData.filter(user =>
            user.building_id === this.adminBuildingId && user.role === 'neighbor'
          );
        }
      } catch (error) {
        console.error('Error fetching users:', error);
        this.showError('Error', 'Failed to load users');
      } finally {
        this.loading = false;
      }
    },
    async fetchBuildings() {
      try {
        const response = await this.$axios.get('/buildings');
        this.buildings = response.data;
      } catch (error) {
        console.error('Error fetching buildings:', error);
      }
    },
    applyFilters() {
      // Filters are applied through computed property
    },
    editUser(user) {
      this.selectedUser = user;
      this.showEditModal = true;
    },
    closeEditModal() {
      this.showEditModal = false;
      this.selectedUser = null;
    },
    handleEditSuccess(data) {
      this.showSuccess('Success', 'User updated successfully');
      this.closeEditModal();
      this.loadUsers();
    },
    handleEditError(message) {
      this.showError('Error', message);
    },
    async deleteUser(user) {
      if (confirm(`Are you sure you want to delete user "${user.name}"?`)) {
        try {
          await this.$axios.delete(`/admin/users/${user.id}`);
          this.showSuccess('Success', 'User deleted successfully');
          this.loadUsers();
        } catch (error) {
          this.showError('Error', error.response?.data?.message || 'Failed to delete user');
        }
      }
    },
    showSuccess(title, message) {
      this.notificationType = 'success';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    showError(title, message) {
      this.notificationType = 'error';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    closeNotification() {
      this.showNotification = false;
    }
  }
};
</script>
