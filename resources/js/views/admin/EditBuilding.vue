<template>
  <dashboard-layout :title="$t('edit_building')">
    <template #sidebar>
      <div v-if="userLoaded">
        <router-link
          to="/admin/expenses"
          class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
          active-class="bg-indigo-50 text-indigo-600"
        >
          {{ $t('expenses') }}
        </router-link>
        <router-link
          to="/admin/incomes"
          class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
          active-class="bg-indigo-50 text-indigo-600"
        >
          {{ $t('incomes') }}
        </router-link>
        <router-link
          to="/admin/users"
          class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
          active-class="bg-indigo-50 text-indigo-600"
        >
          {{ $t('users') }}
        </router-link>
        <router-link
          v-if="isSuperAdmin"
          to="/admin/buildings"
          class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
          active-class="bg-indigo-50 text-indigo-600"
        >
          {{ $t('buildings') }}
        </router-link>
        <router-link
          v-if="!isSuperAdmin"
          to="/admin/my-building"
          class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
          active-class="bg-indigo-50 text-indigo-600"
        >
          {{ $t('my_building') }}
        </router-link>
      </div>
      <div v-else class="text-gray-500 text-sm">
        {{ $t('loading_menu') }}
      </div>
    </template>

    <div v-if="loading" class="text-center py-4">
      <p>{{ $t('loading_building') }}</p>
    </div>

    <div v-else-if="building" class="max-w-2xl mx-auto">
      <div class="bg-white p-6 rounded-lg shadow-sm">
        <building-form
          :building="building"
          :is-edit="true"
          :is-my-building="false"
          @success="handleSuccess"
          @error="handleError"
          @cancel="handleCancel"
        />
      </div>
    </div>

    <div v-else class="text-center py-8">
      <p class="text-red-500">{{ $t('building_not_found') }}</p>
    </div>

    <!-- Notifications -->
    <notification
      :show="showNotification"
      :type="notificationType"
      :title="notificationTitle"
      :message="notificationMessage"
      @close="closeNotification"
    />
  </dashboard-layout>
</template>

<script>
import DashboardLayout from '../../components/DashboardLayout.vue';
import BuildingForm from '../../components/BuildingForm.vue';
import Notification from '../../components/Notification.vue';
import i18nMixin from '../../mixins/i18nMixin.js';

export default {
  mixins: [i18nMixin],
  components: {
    DashboardLayout,
    BuildingForm,
    Notification
  },
  data() {
    return {
      loading: false,
      building: null,
      showNotification: false,
      notificationType: 'info',
      notificationTitle: '',
      notificationMessage: '',
      userLoaded: false,
      isSuperAdmin: false
    };
  },
  async mounted() {
    this.initializeUserFromStorage();
    await this.loadBuilding();
  },
  methods: {
    initializeUserFromStorage() {
      try {
        const userData = JSON.parse(localStorage.getItem('user') || 'null');
        if (userData && userData.role) {
          this.isSuperAdmin = userData.role === 'super_admin';
          this.userLoaded = true;
        }
      } catch (error) {
        console.error('Error parsing user data from localStorage:', error);
      }
    },
    async loadBuilding() {
      this.loading = true;
      try {
        const buildingId = this.$route.params.id;
        const response = await this.$axios.get(`/buildings/${buildingId}`);
        this.building = response.data;
      } catch (error) {
        console.error('Error loading building:', error);
        this.showError(this.$t('error'), this.$t('failed_load_building'));
      } finally {
        this.loading = false;
      }
    },
    handleSuccess(data) {
      this.showSuccess(this.$t('success'), this.$t('building_updated'));
      setTimeout(() => {
        this.$router.push('/admin/buildings');
      }, 1500);
    },
    handleError(message) {
      this.showError(this.$t('error'), message);
    },
    handleCancel() {
      this.$router.push('/admin/buildings');
    },
    showSuccess(title, message) {
      this.notificationType = 'success';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    showError(title, message) {
      this.notificationType = 'error';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    closeNotification() {
      this.showNotification = false;
    }
  }
};
</script>
