<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Page Content -->
    <div class="max-w-6xl mx-auto px-8 py-8">

      <div v-if="loading" class="text-center py-4">
        <p>{{ $t('loading') }}</p>
      </div>

      <div v-else-if="building">
        <!-- Building Info Card -->
        <div class="bg-white p-6 rounded-lg shadow-sm mb-6">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold text-gray-900">{{ $t('building_information') }}</h2>
            <button
              @click="editBuilding"
              class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              {{ $t('edit_building') }}
            </button>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 class="text-sm font-medium text-gray-500">{{ $t('building_name') }}</h3>
              <p class="mt-1 text-sm text-gray-900">{{ building.name }}</p>
            </div>

            <div>
              <h3 class="text-sm font-medium text-gray-500">{{ $t('monthly_fee') }}</h3>
              <p class="mt-1 text-sm text-gray-900">₪{{ parseFloat(building.monthly_fee).toFixed(2) }}</p>
            </div>

            <div>
              <h3 class="text-sm font-medium text-gray-500">{{ $t('address') }}</h3>
              <p class="mt-1 text-sm text-gray-900">{{ building.address || $t('not_specified') }}</p>
            </div>

            <div>
              <h3 class="text-sm font-medium text-gray-500">{{ $t('city') }}</h3>
              <p class="mt-1 text-sm text-gray-900">{{ building.city || $t('not_specified') }}</p>
            </div>

            <div>
              <h3 class="text-sm font-medium text-gray-500">{{ $t('country') }}</h3>
              <p class="mt-1 text-sm text-gray-900">{{ building.country || $t('not_specified') }}</p>
            </div>

            <div>
              <h3 class="text-sm font-medium text-gray-500">{{ $t('postal_code') }}</h3>
              <p class="mt-1 text-sm text-gray-900">{{ building.postal_code || $t('not_specified') }}</p>
            </div>
          </div>

          <div v-if="building.description" class="mt-6">
            <h3 class="text-sm font-medium text-gray-500">{{ $t('description') }}</h3>
            <p class="mt-1 text-sm text-gray-900">{{ building.description }}</p>
          </div>
        </div>

        <!-- Monthly Expense Generation -->
        <div class="bg-white p-6 rounded-lg shadow-sm">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">{{ $t('monthly_expense_generation') }}</h2>
          <p class="text-sm text-gray-600 mb-4">
            {{ $t('generate_monthly_expenses_description') }}
            {{ $t('each_neighbor_charged') }} ₪{{ parseFloat(building.monthly_fee).toFixed(2) }}.
          </p>

          <div class="flex items-center space-x-4">
            <button
              @click="generateMonthlyExpenses"
              :disabled="generatingExpenses"
              class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:bg-gray-400"
            >
              {{ generatingExpenses ? $t('generating') : `${$t('generate_monthly_expenses')} (₪${parseFloat(building.monthly_fee).toFixed(2)})` }}
            </button>
          </div>
        </div>
      </div>

      <div v-else class="text-center py-8">
        <p class="text-red-500">{{ $t('no_building_assigned') }}</p>
      </div>

      <!-- Edit Modal -->
      <div v-if="showEditModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4">
          <div class="flex justify-between items-center p-6 border-b">
            <h2 class="text-xl font-semibold text-gray-900">{{ $t('edit_building') }}</h2>
            <button @click="closeEditModal" class="text-gray-400 hover:text-gray-600">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>

          <!-- Modal body -->
          <div class="p-6">
            <building-form
              :building="building"
              :is-edit="true"
              :is-my-building="true"
              @success="handleEditSuccess"
              @error="handleEditError"
              @cancel="closeEditModal"
            />
          </div>
        </div>
      </div>

      <!-- Notifications -->
      <notification
        :show="showNotification"
        :type="notificationType"
        :title="notificationTitle"
        :message="notificationMessage"
        @close="closeNotification"
      />
    </div>
  </div>
</template>

<script>
import BuildingForm from '../../components/BuildingForm.vue';
import Notification from '../../components/Notification.vue';
import i18nMixin from '../../mixins/i18nMixin.js';

export default {
  mixins: [i18nMixin],
  components: {
    BuildingForm,
    Notification
  },
  data() {
    return {
      loading: false,
      building: null,
      showEditModal: false,
      generatingExpenses: false,
      showNotification: false,
      notificationType: 'info',
      notificationTitle: '',
      notificationMessage: '',
      userLoaded: false,
      isSuperAdmin: false
    };
  },
  mounted() {
    this.initializeUserFromStorage();
    this.loadBuilding();
  },
  methods: {
    initializeUserFromStorage() {
      try {
        const userData = JSON.parse(localStorage.getItem('user') || 'null');
        if (userData && userData.role) {
          this.isSuperAdmin = userData.role === 'super_admin';
          this.userLoaded = true;
        }
      } catch (error) {
        console.error('Error parsing user data from localStorage:', error);
      }
    },
    async loadBuilding() {
      this.loading = true;
      try {
        const response = await this.$axios.get('/my-building');
        this.building = response.data;
      } catch (error) {
        console.error('Error loading building:', error);
        if (error.response?.status !== 404) {
          this.showError(this.$t('error'), this.$t('failed_load_building_info'));
        }
      } finally {
        this.loading = false;
      }
    },
    editBuilding() {
      this.showEditModal = true;
    },
    closeEditModal() {
      this.showEditModal = false;
    },
    handleEditSuccess(data) {
      this.showSuccess(this.$t('success'), this.$t('building_updated'));
      this.closeEditModal();
      this.loadBuilding();
    },
    handleEditError(message) {
      this.showError(this.$t('error'), message);
    },
    async generateMonthlyExpenses() {
      const currentMonth = new Date().getMonth() + 1;
      const currentYear = new Date().getFullYear();
      const formattedMonth = currentMonth.toString().padStart(2, '0');
      const monthlyFee = parseFloat(this.building.monthly_fee).toFixed(2);

      if (confirm(this.$t('confirm_generate_monthly_expenses', { fee: monthlyFee, month: formattedMonth, year: currentYear }))) {
        this.generatingExpenses = true;
        try {
          await this.$axios.post('/expenses/generate-monthly', {
            month: formattedMonth,
            year: currentYear.toString()
          });

          this.showSuccess(this.$t('generated'), this.$t('monthly_expenses_generated'));
        } catch (error) {
          this.showError(this.$t('generation_failed'), error.response?.data?.message || this.$t('failed_generate_monthly_expenses'));
        } finally {
          this.generatingExpenses = false;
        }
      }
    },
    showSuccess(title, message) {
      this.notificationType = 'success';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    showError(title, message) {
      this.notificationType = 'error';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    closeNotification() {
      this.showNotification = false;
    }
  }
};
</script>
