<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Page Content -->
    <div class="max-w-6xl mx-auto px-8 py-8">

      <!-- Filters -->
      <div class="bg-white p-6 rounded-lg shadow-sm mb-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">{{ $t('from_date') }}</label>
            <input
              type="date"
              v-model="filters.date_from"
              class="w-full px-3 py-2 border border-gray-300 rounded-md"
              :dir="$isRTL() ? 'rtl' : 'ltr'"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">{{ $t('to_date') }}</label>
            <input
              type="date"
              v-model="filters.date_to"
              class="w-full px-3 py-2 border border-gray-300 rounded-md"
              :dir="$isRTL() ? 'rtl' : 'ltr'"
            />
          </div>

          <div class="flex items-end">
            <button
              @click="applyFilters"
              class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              {{ $t('apply_filters') }}
            </button>
          </div>
        </div>

        <div class="mt-4 flex justify-between items-center">
          <router-link
            to="/admin/incomes/create"
            class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
          >
            {{ $t('record_new_income') }}
          </router-link>
        </div>
      </div>

      <!-- Summary Cards -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div class="bg-white p-6 rounded-lg shadow-sm">
          <h3 class="text-lg font-semibold text-gray-700">{{ $t('total_income') }}</h3>
          <p class="text-3xl font-bold text-green-600">₪{{ totalIncome.toFixed(2) }}</p>
        </div>
        <div class="bg-white p-6 rounded-lg shadow-sm">
          <h3 class="text-lg font-semibold text-gray-700">{{ $t('this_month') }}</h3>
          <p class="text-3xl font-bold text-blue-600">₪{{ thisMonthIncome.toFixed(2) }}</p>
        </div>
        <div class="bg-white p-6 rounded-lg shadow-sm">
          <h3 class="text-lg font-semibold text-gray-700">{{ $t('last_30_days') }}</h3>
          <p class="text-3xl font-bold text-purple-600">₪{{ last30DaysIncome.toFixed(2) }}</p>
        </div>
      </div>

      <!-- Income Table -->
      <data-table
        :title="$t('income_records')"
        :columns="columns"
        :items="incomes"
        :loading="loading"
      >
        <template #actions="{ item }">
          <div class="flex space-x-2">
            <button
              @click="editIncome(item)"
              class="text-indigo-600 hover:text-indigo-900"
            >
              {{ $t('edit') }}
            </button>
            <button
              @click="deleteIncome(item)"
              class="text-red-600 hover:text-red-900"
            >
              {{ $t('delete') }}
            </button>
          </div>
        </template>
      </data-table>

      <!-- Pagination -->
      <div class="mt-6 flex justify-between items-center">
        <div class="text-sm text-gray-700">
          {{ $t('showing_results') }} {{ pagination.from }} {{ $t('to') }} {{ pagination.to }} {{ $t('of') }} {{ pagination.total }} {{ $t('results') }}
        </div>
        <div class="flex space-x-2">
          <button
            @click="previousPage"
            :disabled="pagination.currentPage === 1"
            class="px-3 py-1 border rounded disabled:opacity-50"
          >
            {{ $t('previous') }}
          </button>
          <button
            @click="nextPage"
            :disabled="pagination.currentPage === pagination.lastPage"
            class="px-3 py-1 border rounded disabled:opacity-50"
          >
            {{ $t('next') }}
          </button>
        </div>
      </div>

      <!-- Edit Modal -->
      <div v-if="showEditModal" class="fixed inset-0 z-50 overflow-y-auto">
        <!-- Background overlay -->
        <div class="fixed inset-0 bg-black bg-opacity-50" @click="closeEditModal"></div>

        <!-- Modal content -->
        <div class="flex items-center justify-center min-h-screen p-4">
          <div class="relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-screen overflow-y-auto">
            <!-- Close button -->
            <button
              @click="closeEditModal"
              class="absolute top-4 right-4 text-gray-400 hover:text-gray-600 z-10"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>

            <!-- Modal header -->
            <div class="px-6 py-4 border-b border-gray-200">
              <h3 class="text-lg font-semibold text-gray-900">
                {{ $t('edit_income') }}
              </h3>
            </div>

            <!-- Modal body -->
            <div class="p-6">
              <income-form
                :income="selectedIncome"
                :is-edit="true"
                @success="handleEditSuccess"
                @error="handleEditError"
                @cancel="closeEditModal"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Notifications -->
      <notification
        :show="showNotification"
        :type="notificationType"
        :title="notificationTitle"
        :message="notificationMessage"
        @close="closeNotification"
      />
    </div>
  </div>
</template>

<script>
import DataTable from '../../components/DataTable.vue';
import IncomeForm from '../../components/IncomeForm.vue';
import Notification from '../../components/Notification.vue';
import i18nMixin from '../../mixins/i18nMixin.js';

export default {
  mixins: [i18nMixin],
  components: {
    DataTable,
    IncomeForm,
    Notification
  },
  data() {
    return {
      loading: false,
      incomes: [],
      selectedIncome: null,
      showEditModal: false,
      showNotification: false,
      notificationType: 'info',
      notificationTitle: '',
      notificationMessage: '',
      user: null,
      isSuperAdmin: false,
      userLoaded: false,
      filters: {
        date_from: '',
        date_to: ''
      },
      pagination: {
        currentPage: 1,
        lastPage: 1,
        from: 0,
        to: 0,
        total: 0
      },
      columns: []
    };
  },
  computed: {
    totalIncome() {
      return this.incomes.reduce((total, income) => total + parseFloat(income.amount), 0);
    },
    thisMonthIncome() {
      const currentMonth = new Date().getMonth() + 1;
      const currentYear = new Date().getFullYear();
      return this.incomes
        .filter(income => {
          const paymentDate = new Date(income.payment_date);
          return paymentDate.getMonth() + 1 === currentMonth &&
                 paymentDate.getFullYear() === currentYear;
        })
        .reduce((total, income) => total + parseFloat(income.amount), 0);
    },
    last30DaysIncome() {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      return this.incomes
        .filter(income => {
          const paymentDate = new Date(income.payment_date);
          return paymentDate >= thirtyDaysAgo;
        })
        .reduce((total, income) => total + parseFloat(income.amount), 0);
    }
  },
  async created() {
    this.initializeColumns();
    this.initializeUserFromStorage();
    await this.fetchUser();
    this.loadIncomes();
  },
  methods: {
    initializeColumns() {
      this.columns = [
        { key: 'user.name', label: this.$t('neighbor') },
        { key: 'user.apartment_number', label: this.$t('apartment') },
        { key: 'amount', label: this.$t('amount') },
        { key: 'payment_date', label: this.$t('payment_date') },
        { key: 'payment_method', label: this.$t('method') },
        { key: 'notes', label: this.$t('notes') }
      ];
    },
    initializeUserFromStorage() {
      try {
        const userData = JSON.parse(localStorage.getItem('user') || 'null');
        if (userData && userData.role) {
          this.isSuperAdmin = userData.role === 'super_admin';
          this.userLoaded = true;
        }
      } catch (error) {
        console.error('Error parsing user data from localStorage:', error);
      }
    },
    async fetchUser() {
      try {
        const response = await this.$axios.get('/user');
        this.user = response.data;
        this.isSuperAdmin = this.user.role === 'super_admin';
        this.userLoaded = true;
        localStorage.setItem('user', JSON.stringify(this.user));
      } catch (error) {
        console.error('Error fetching user:', error);
      }
    },
    async loadIncomes() {
      this.loading = true;
      try {
        const response = await this.$axios.get('/incomes', {
          params: {
            page: this.pagination.currentPage,
            ...this.filters
          }
        });
        
        this.incomes = response.data.data;
        this.pagination = {
          currentPage: response.data.current_page,
          lastPage: response.data.last_page,
          from: response.data.from,
          to: response.data.to,
          total: response.data.total
        };
      } catch (error) {
        this.showError(this.$t('error_loading_incomes'));
      } finally {
        this.loading = false;
      }
    },
    applyFilters() {
      this.pagination.currentPage = 1;
      this.loadIncomes();
    },
    previousPage() {
      if (this.pagination.currentPage > 1) {
        this.pagination.currentPage--;
        this.loadIncomes();
      }
    },
    nextPage() {
      if (this.pagination.currentPage < this.pagination.lastPage) {
        this.pagination.currentPage++;
        this.loadIncomes();
      }
    },
    editIncome(income) {
      this.selectedIncome = income;
      this.showEditModal = true;
    },
    async deleteIncome(income) {
      if (confirm(this.$t('confirm_delete_income'))) {
        try {
          await this.$axios.delete(`/incomes/${income.id}`);
          this.showSuccess(this.$t('deleted'), this.$t('income_deleted'));
          this.loadIncomes();
        } catch (error) {
          this.showError(this.$t('delete_failed'), this.$t('failed_delete_income'));
        }
      }
    },
    handleEditSuccess() {
      this.showSuccess(this.$t('updated'), this.$t('income_updated'));
      this.closeEditModal();
      this.loadIncomes();
    },
    handleEditError(message) {
      this.showError(this.$t('update_failed'), message);
    },
    closeEditModal() {
      this.showEditModal = false;
      this.selectedIncome = null;
    },
    showSuccess(title, message) {
      this.notificationType = 'success';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    showError(title, message) {
      this.notificationType = 'error';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    closeNotification() {
      this.showNotification = false;
    }
  }
};
</script>
