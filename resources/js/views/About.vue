<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Hero Section -->
    <section class="bg-gradient-to-br from-blue-900 to-blue-700 text-white py-20">
      <div class="max-w-6xl mx-auto px-8 text-center">
        <h1 class="text-4xl lg:text-6xl font-bold mb-6">{{ $t('about_us_title') }}</h1>
        <p class="text-xl lg:text-2xl text-blue-100 max-w-3xl mx-auto">{{ $t('about_us_subtitle') }}</p>
      </div>
    </section>

    <!-- Mission Section -->
    <section class="py-20">
      <div class="max-w-6xl mx-auto px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">{{ $t('about_mission_title') }}</h2>
            <p class="text-lg text-gray-600 leading-relaxed">{{ $t('about_mission_text') }}</p>
          </div>
          <div class="flex justify-center">
            <div class="w-full max-w-md">
              <svg viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-full h-auto">
                <!-- Mission Illustration -->
                <rect x="50" y="100" width="300" height="150" rx="10" fill="#F59E42" stroke="#1E3A8A" stroke-width="3"/>
                <rect x="70" y="120" width="40" height="30" fill="#87CEEB" stroke="#1E3A8A" stroke-width="2"/>
                <rect x="130" y="120" width="40" height="30" fill="#87CEEB" stroke="#1E3A8A" stroke-width="2"/>
                <rect x="190" y="120" width="40" height="30" fill="#87CEEB" stroke="#1E3A8A" stroke-width="2"/>
                <rect x="250" y="120" width="40" height="30" fill="#87CEEB" stroke="#1E3A8A" stroke-width="2"/>
                <rect x="310" y="120" width="40" height="30" fill="#87CEEB" stroke="#1E3A8A" stroke-width="2"/>
                <circle cx="200" cy="50" r="30" fill="#32CD32" stroke="#1E3A8A" stroke-width="2"/>
                <text x="200" y="60" text-anchor="middle" fill="#fff" font-size="20" font-weight="bold">✓</text>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Vision Section -->
    <section class="py-20 bg-white">
      <div class="max-w-6xl mx-auto px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div class="order-2 lg:order-1 flex justify-center">
            <div class="w-full max-w-md">
              <svg viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-full h-auto">
                <!-- Vision Illustration -->
                <circle cx="200" cy="150" r="100" fill="#E0E7FF" stroke="#1E3A8A" stroke-width="3"/>
                <rect x="150" y="100" width="100" height="100" rx="8" fill="#F59E42" stroke="#1E3A8A" stroke-width="2"/>
                <rect x="170" y="120" width="15" height="20" fill="#87CEEB" stroke="#1E3A8A" stroke-width="1"/>
                <rect x="190" y="120" width="15" height="20" fill="#87CEEB" stroke="#1E3A8A" stroke-width="1"/>
                <rect x="210" y="120" width="15" height="20" fill="#87CEEB" stroke="#1E3A8A" stroke-width="1"/>
                <rect x="170" y="150" width="15" height="20" fill="#87CEEB" stroke="#1E3A8A" stroke-width="1"/>
                <rect x="210" y="150" width="15" height="20" fill="#87CEEB" stroke="#1E3A8A" stroke-width="1"/>
                <rect x="190" y="150" width="15" height="30" fill="#8B4513" stroke="#1E3A8A" stroke-width="1"/>
                <path d="M100 50 L200 20 L300 50" stroke="#FFD700" stroke-width="4" fill="none"/>
              </svg>
            </div>
          </div>
          <div class="order-1 lg:order-2">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">{{ $t('about_vision_title') }}</h2>
            <p class="text-lg text-gray-600 leading-relaxed">{{ $t('about_vision_text') }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Values Section -->
    <section class="py-20 bg-gray-50">
      <div class="max-w-6xl mx-auto px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">{{ $t('about_values_title') }}</h2>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <!-- Transparency -->
          <div class="bg-white rounded-lg p-8 shadow-lg text-center hover:shadow-xl transition-shadow duration-300">
            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-4">{{ $t('about_transparency') }}</h3>
            <p class="text-gray-600">{{ $t('about_transparency_desc') }}</p>
          </div>

          <!-- Efficiency -->
          <div class="bg-white rounded-lg p-8 shadow-lg text-center hover:shadow-xl transition-shadow duration-300">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-4">{{ $t('about_efficiency') }}</h3>
            <p class="text-gray-600">{{ $t('about_efficiency_desc') }}</p>
          </div>

          <!-- Innovation -->
          <div class="bg-white rounded-lg p-8 shadow-lg text-center hover:shadow-xl transition-shadow duration-300">
            <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-4">{{ $t('about_innovation') }}</h3>
            <p class="text-gray-600">{{ $t('about_innovation_desc') }}</p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';

export default {
  name: 'About',
  mixins: [i18nMixin]
};
</script>

<style scoped>
/* Custom animations */
.fade-in {
  animation: fadeIn 0.8s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
