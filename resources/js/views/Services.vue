<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Hero Section -->
    <section class="bg-gradient-to-br from-blue-900 to-blue-700 text-white py-20">
      <div class="max-w-6xl mx-auto px-8 text-center">
        <h1 class="text-4xl lg:text-6xl font-bold mb-6">{{ $t('services_title') }}</h1>
        <p class="text-xl lg:text-2xl text-blue-100 max-w-3xl mx-auto">{{ $t('services_subtitle') }}</p>
      </div>
    </section>

    <!-- Services Grid -->
    <section class="py-20">
      <div class="max-w-6xl mx-auto px-8">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          
          <!-- Expense Management -->
          <div class="bg-white rounded-lg p-8 shadow-lg hover:shadow-xl transition-shadow duration-300">
            <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-6">
              <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-4">{{ $t('service_expense_title') }}</h3>
            <p class="text-gray-600">{{ $t('service_expense_desc') }}</p>
          </div>

          <!-- Income Management -->
          <div class="bg-white rounded-lg p-8 shadow-lg hover:shadow-xl transition-shadow duration-300">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-6">
              <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-4">{{ $t('service_income_title') }}</h3>
            <p class="text-gray-600">{{ $t('service_income_desc') }}</p>
          </div>

          <!-- Resident Management -->
          <div class="bg-white rounded-lg p-8 shadow-lg hover:shadow-xl transition-shadow duration-300">
            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-6">
              <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-4">{{ $t('service_residents_title') }}</h3>
            <p class="text-gray-600">{{ $t('service_residents_desc') }}</p>
          </div>

          <!-- Reports and Statistics -->
          <div class="bg-white rounded-lg p-8 shadow-lg hover:shadow-xl transition-shadow duration-300">
            <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mb-6">
              <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-4">{{ $t('service_reports_title') }}</h3>
            <p class="text-gray-600">{{ $t('service_reports_desc') }}</p>
          </div>

          <!-- Notification System -->
          <div class="bg-white rounded-lg p-8 shadow-lg hover:shadow-xl transition-shadow duration-300">
            <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mb-6">
              <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM11 19H6.5a2.5 2.5 0 010-5H11m0 5a2 2 0 01-2-2V9a2 2 0 012-2m0 10a2 2 0 002-2V9a2 2 0 00-2-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v2M7 7V5a2 2 0 012-2h2a2 2 0 012 2v2m-6 0h6"></path>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-4">{{ $t('service_notifications_title') }}</h3>
            <p class="text-gray-600">{{ $t('service_notifications_desc') }}</p>
          </div>

          <!-- Security and Protection -->
          <div class="bg-white rounded-lg p-8 shadow-lg hover:shadow-xl transition-shadow duration-300">
            <div class="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mb-6">
              <svg class="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-4">{{ $t('service_security_title') }}</h3>
            <p class="text-gray-600">{{ $t('service_security_desc') }}</p>
          </div>

        </div>
      </div>
    </section>

    <!-- Call to Action -->
    <section class="py-20 bg-blue-900 text-white">
      <div class="max-w-4xl mx-auto px-8 text-center">
        <h2 class="text-3xl lg:text-4xl font-bold mb-6">{{ $t('welcome') }}</h2>
        <p class="text-xl text-blue-100 mb-8">{{ $t('building_management_short') }}</p>
        <router-link 
          to="/register" 
          class="inline-block bg-white text-blue-900 font-semibold px-8 py-4 rounded-full shadow-lg text-lg transition-all duration-300 transform hover:scale-105 hover:shadow-xl"
        >
          {{ $t('get_started') }}
        </router-link>
      </div>
    </section>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';

export default {
  name: 'Services',
  mixins: [i18nMixin]
};
</script>

<style scoped>
/* Custom animations */
.fade-in {
  animation: fadeIn 0.8s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
