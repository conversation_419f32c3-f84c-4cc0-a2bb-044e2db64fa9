<template>
  <dashboard-layout :title="$t('my_profile')">
    <template #sidebar>
      <router-link
        to="/neighbor"
        class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
        active-class="bg-indigo-50 text-indigo-600"
      >
        {{ $t('my_financial_summary') }}
      </router-link>
      <router-link
        to="/neighbor/profile"
        class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
        active-class="bg-indigo-50 text-indigo-600"
      >
        {{ $t('my_profile') }}
      </router-link>
    </template>

    <!-- Profile Information -->
    <div class="mb-6">
      <div class="bg-white p-4 lg:p-6 rounded-lg shadow-sm">
        <h3 class="text-lg font-semibold text-gray-700 mb-4">{{ $t('profile_information') }}</h3>
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-500">{{ $t('name') }}</label>
            <p class="text-gray-900">{{ user?.name || 'N/A' }}</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-500">{{ $t('apartment_number') }}</label>
            <p class="text-gray-900">{{ user?.apartment_number || 'N/A' }}</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-500">{{ $t('email') }}</label>
            <p class="text-gray-900">{{ user?.email || 'N/A' }}</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-500">{{ $t('role') }}</label>
            <p class="text-gray-900">{{ $t('neighbor') }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Update Profile Form -->
    <div class="bg-white p-4 lg:p-6 rounded-lg shadow-sm">
      <profile-form
        :user="user"
        @success="handleUpdateSuccess"
        @error="handleUpdateError"
        @cancel="handleCancel"
      />
    </div>

    <!-- Notifications -->
    <notification
      :show="showNotification"
      :type="notificationType"
      :title="notificationTitle"
      :message="notificationMessage"
      @close="closeNotification"
    />
  </dashboard-layout>
</template>

<script>
import DashboardLayout from '../../components/DashboardLayout.vue';
import ProfileForm from '../../components/ProfileForm.vue';
import Notification from '../../components/Notification.vue';
import i18nMixin from '../../mixins/i18nMixin.js';

export default {
  mixins: [i18nMixin],
  components: {
    DashboardLayout,
    ProfileForm,
    Notification
  },
  data() {
    return {
      user: null,
      showNotification: false,
      notificationType: 'info',
      notificationTitle: '',
      notificationMessage: ''
    };
  },
  async created() {
    await this.loadUserData();
  },
  methods: {
    async loadUserData() {
      try {
        // Get user data from localStorage first
        const storedUser = JSON.parse(localStorage.getItem('user') || '{}');
        this.user = storedUser;

        // Fetch fresh user data from API
        const response = await this.$axios.get('/user');
        this.user = response.data;
        
        // Update localStorage with fresh data
        localStorage.setItem('user', JSON.stringify(response.data));
      } catch (error) {
        console.error('Error loading user data:', error);
        this.showError(this.$t('error'), this.$t('failed_to_load_profile'));
      }
    },
    handleUpdateSuccess(data) {
      this.showSuccess(this.$t('success'), data.message || this.$t('profile_updated_successfully'));
      // Update local user data
      this.user = { ...this.user, email: data.user.email };
    },
    handleUpdateError(message) {
      this.showError(this.$t('error'), message);
    },
    handleCancel() {
      // Navigate back to dashboard
      this.$router.push('/neighbor');
    },
    showSuccess(title, message) {
      this.notificationType = 'success';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    showError(title, message) {
      this.notificationType = 'error';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    closeNotification() {
      this.showNotification = false;
    }
  }
};
</script>
