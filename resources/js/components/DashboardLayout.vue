<template>
  <div class="min-h-screen bg-gray-100">
    <!-- Mobile menu overlay -->
    <div v-if="sidebarOpen" class="fixed inset-0 z-40 lg:hidden">
      <div class="fixed inset-0 bg-gray-600 bg-opacity-75" @click="sidebarOpen = false"></div>
    </div>

    <div class="flex">
      <!-- Mobile sidebar -->
      <div class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:hidden"
           :class="sidebarOpen ? 'translate-x-0' : '-translate-x-full'">
        <div class="p-4">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-bold text-gray-800">{{ title }}</h2>
            <button @click="sidebarOpen = false" class="text-gray-500 hover:text-gray-700">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
          <nav class="space-y-2">
            <slot name="sidebar"></slot>
          </nav>
        </div>
      </div>

      <!-- Desktop sidebar -->
      <div class="hidden lg:block w-64 bg-white shadow-lg h-screen fixed">
        <div class="p-4">
          <h2 class="text-xl font-bold text-gray-800 mb-6">{{ title }}</h2>
          <nav class="space-y-2">
            <slot name="sidebar"></slot>
          </nav>
        </div>
      </div>

      <!-- Main Content -->
      <div class="flex-1 lg:ml-64">
        <!-- Mobile header -->
        <div class="lg:hidden bg-white shadow-sm border-b border-gray-200 px-4 py-3">
          <div class="flex items-center justify-between">
            <button @click="sidebarOpen = true" class="text-gray-500 hover:text-gray-700">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
              </svg>
            </button>
            <h1 class="text-lg font-semibold text-gray-800">{{ title }}</h1>
            <div class="w-6"></div> <!-- Spacer for centering -->
          </div>
        </div>

        <!-- Content -->
        <div class="p-4 lg:p-8">
          <div class="max-w-7xl mx-auto">
            <slot></slot>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DashboardLayout',
  props: {
    title: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      sidebarOpen: false
    };
  },
  mounted() {
    // Close sidebar when clicking outside on mobile
    document.addEventListener('click', this.handleClickOutside);
  },
  beforeUnmount() {
    document.removeEventListener('click', this.handleClickOutside);
  },
  methods: {
    handleClickOutside(event) {
      // This is handled by the overlay click in the template
    }
  }
};
</script>