<template>
  <div>
    <h2 v-if="!isEdit" class="text-xl font-semibold text-gray-900 mb-6">{{ $t('building_information') }}</h2>

    <form @submit.prevent="handleSubmit" class="space-y-4 lg:space-y-6">
      <div>
        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">{{ $t('building_name') }}</label>
        <input
          type="text"
          id="name"
          v-model="formData.name"
          class="w-full px-3 py-2 text-base lg:text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          required
        />
      </div>

      <div>
        <label for="address" class="block text-sm font-medium text-gray-700 mb-2">{{ $t('address') }}</label>
        <input
          type="text"
          id="address"
          v-model="formData.address"
          class="w-full px-3 py-2 text-base lg:text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      <!-- City, Country, and Postal Code in a responsive grid -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
        <div>
          <label for="city" class="block text-sm font-medium text-gray-700 mb-2">{{ $t('city') }}</label>
          <input
            type="text"
            id="city"
            v-model="formData.city"
            class="w-full px-3 py-2 text-base lg:text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <label for="country" class="block text-sm font-medium text-gray-700 mb-2">{{ $t('country') }}</label>
          <input
            type="text"
            id="country"
            v-model="formData.country"
            class="w-full px-3 py-2 text-base lg:text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <label for="postal_code" class="block text-sm font-medium text-gray-700 mb-2">{{ $t('postal_code') }}</label>
          <input
            type="text"
            id="postal_code"
            v-model="formData.postal_code"
            class="w-full px-3 py-2 text-base lg:text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      <div>
        <label for="monthly_fee" class="block text-sm font-medium text-gray-700 mb-2">{{ $t('monthly_fee') }} (₪)</label>
        <input
          type="number"
          id="monthly_fee"
          v-model="formData.monthly_fee"
          step="0.01"
          min="0"
          class="w-full px-3 py-2 text-base lg:text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          required
        />
        <p class="mt-1 text-sm text-gray-500">{{ $t('monthly_fee_description') }}</p>
      </div>

      <div>
        <label for="description" class="block text-sm font-medium text-gray-700 mb-2">{{ $t('description') }}</label>
        <textarea
          id="description"
          v-model="formData.description"
          rows="3"
          class="w-full px-3 py-2 text-base lg:text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        ></textarea>
      </div>

      <div class="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3 pt-4">
        <button
          type="button"
          @click="$emit('cancel')"
          class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          {{ $t('cancel') }}
        </button>
        <button
          type="submit"
          :disabled="processing"
          class="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {{ processing ? $t('saving') : (isEdit ? $t('update_building') : $t('create_building')) }}
        </button>
      </div>
    </form>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';

export default {
  name: 'BuildingForm',
  mixins: [i18nMixin],
  props: {
    building: {
      type: Object,
      default: () => ({
        name: '',
        address: '',
        city: '',
        country: '',
        postal_code: '',
        description: '',
        monthly_fee: 70.00
      })
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    isMyBuilding: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      processing: false,
      formData: {
        name: '',
        address: '',
        city: '',
        country: '',
        postal_code: '',
        description: '',
        monthly_fee: 70.00
      }
    };
  },
  watch: {
    building: {
      handler(newBuilding) {
        if (newBuilding) {
          this.formData = {
            name: newBuilding.name || '',
            address: newBuilding.address || '',
            city: newBuilding.city || '',
            country: newBuilding.country || '',
            postal_code: newBuilding.postal_code || '',
            description: newBuilding.description || '',
            monthly_fee: newBuilding.monthly_fee || 70.00
          };
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    // Initialize form data
    if (this.building) {
      this.formData = {
        name: this.building.name || '',
        address: this.building.address || '',
        city: this.building.city || '',
        country: this.building.country || '',
        postal_code: this.building.postal_code || '',
        description: this.building.description || '',
        monthly_fee: this.building.monthly_fee || 70.00
      };
    }
  },
  methods: {
    async handleSubmit() {
      this.processing = true;
      try {
        const submitData = {
          ...this.formData
        };

        let url, method;

        if (this.isEdit) {
          if (this.isMyBuilding) {
            // Admin editing their own building
            url = '/my-building';
            method = 'put';
          } else {
            // Super admin editing any building
            url = `/buildings/${this.building.id}`;
            method = 'put';
          }
        } else {
          // Creating new building (super admin only)
          url = '/buildings';
          method = 'post';
        }

        const response = await this.$axios[method](url, submitData);
        this.$emit('success', response.data);
      } catch (error) {
        this.$emit('error', error.response?.data?.message || this.$t('failed_save_building'));
      } finally {
        this.processing = false;
      }
    }
  }
};
</script>
