<template>
  <div class="relative">
    <button
      @click="toggleLanguage"
      class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
      :title="currentLocale === 'ar' ? 'Switch to English' : 'التبديل إلى العربية'"
    >
      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
      </svg>
      <span class="font-semibold">
        {{ currentLocale === 'ar' ? 'EN' : 'ع' }}
      </span>
    </button>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';

export default {
  name: 'LanguageToggle',
  mixins: [i18nMixin],
  data() {
    return {
      currentLocale: this.$locale()
    };
  },
  mounted() {
    // Listen for locale changes
    window.addEventListener('localeChanged', this.handleLocaleChange);
    window.addEventListener('forceUpdate', this.handleForceUpdate);
  },
  beforeUnmount() {
    window.removeEventListener('localeChanged', this.handleLocaleChange);
    window.removeEventListener('forceUpdate', this.handleForceUpdate);
  },
  methods: {
    toggleLanguage() {
      const newLocale = this.currentLocale === 'ar' ? 'en' : 'ar';
      // Update the locale using the i18n system
      this.$setLocale(newLocale);
      // Update local state
      this.currentLocale = newLocale;
    },
    handleLocaleChange(event) {
      this.currentLocale = event.detail.locale;
      this.$forceUpdate();
    },
    handleForceUpdate() {
      this.currentLocale = this.$locale();
      this.$forceUpdate();
    }
  }
};
</script>
