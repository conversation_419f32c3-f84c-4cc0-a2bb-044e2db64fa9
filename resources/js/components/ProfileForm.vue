<template>
  <div>
    <h2 class="text-xl font-semibold text-gray-900 mb-6">{{ $t('update_profile') }}</h2>

    <form @submit.prevent="handleSubmit" class="space-y-4 lg:space-y-6">
      <div>
        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">{{ $t('email') }}</label>
        <input
          type="email"
          id="email"
          v-model="formData.email"
          class="w-full px-3 py-2 text-base lg:text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          required
        />
      </div>

      <div>
        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
          {{ $t('new_password') }} ({{ $t('leave_blank_to_keep_current') }})
        </label>
        <input
          type="password"
          id="password"
          v-model="formData.password"
          class="w-full px-3 py-2 text-base lg:text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          minlength="8"
        />
      </div>

      <div>
        <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-2">{{ $t('confirm_new_password') }}</label>
        <input
          type="password"
          id="password_confirmation"
          v-model="formData.password_confirmation"
          class="w-full px-3 py-2 text-base lg:text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      <div class="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3 pt-4">
        <button
          type="button"
          @click="$emit('cancel')"
          class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          {{ $t('cancel') }}
        </button>
        <button
          type="submit"
          :disabled="processing"
          class="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {{ processing ? $t('updating') : $t('update_profile') }}
        </button>
      </div>
    </form>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';

export default {
  name: 'ProfileForm',
  mixins: [i18nMixin],
  props: {
    user: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      processing: false,
      formData: {
        email: '',
        password: '',
        password_confirmation: ''
      }
    };
  },
  watch: {
    user: {
      handler(newUser) {
        if (newUser) {
          this.formData.email = newUser.email || '';
          this.formData.password = '';
          this.formData.password_confirmation = '';
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    // Initialize form data
    if (this.user) {
      this.formData.email = this.user.email || '';
      this.formData.password = '';
      this.formData.password_confirmation = '';
    }
  },
  methods: {
    async handleSubmit() {
      // Validate password confirmation if password is provided
      if (this.formData.password && this.formData.password !== this.formData.password_confirmation) {
        this.$emit('error', this.$t('password_confirmation_mismatch'));
        return;
      }

      this.processing = true;
      try {
        const submitData = {
          email: this.formData.email
        };

        // Only include password if it's provided
        if (this.formData.password) {
          submitData.password = this.formData.password;
          submitData.password_confirmation = this.formData.password_confirmation;
        }

        const response = await this.$axios.put('/profile', submitData);

        // Update user data in localStorage
        const updatedUser = { ...this.user, email: this.formData.email };
        localStorage.setItem('user', JSON.stringify(updatedUser));

        this.$emit('success', response.data);
      } catch (error) {
        this.$emit('error', error.response?.data?.message || this.$t('failed_to_update_profile'));
      } finally {
        this.processing = false;
      }
    }
  }
};
</script>
