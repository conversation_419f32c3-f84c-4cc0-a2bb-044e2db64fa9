<template>
    <div class="min-h-screen" :class="isGuestPage ? 'bg-gray-50' : 'bg-gray-100'">
        <!-- Authenticated User Navigation -->
        <nav v-if="isAuthenticated" :key="currentLocale" class="w-full bg-white shadow-lg">
            <div class="max-w-6xl mx-auto px-8 py-4">
                <div class="flex justify-between items-center">
                    <!-- Left - App Name -->
                    <router-link to="/" class="text-xl lg:text-2xl font-bold text-blue-900 hover:text-blue-700 transition-colors">
                        <img src="/images/logo.png" alt="Logo" class="h-10" />
                    </router-link>

                    <!-- Center - Navigation Menu (Desktop) -->
                    <div class="hidden md:flex space-x-8 text-lg font-medium text-gray-600" :class="$isRTL() ? 'space-x-reverse' : ''">
                        <router-link
                            v-if="isAdmin"
                            to="/admin"
                            class="hover:text-gray-900 transition-colors"
                            active-class="text-blue-600 font-semibold"
                        >
                            {{ $t('admin_dashboard') }}
                        </router-link>
                        <router-link
                            v-if="isAdmin"
                            to="/admin/expenses"
                            class="hover:text-gray-900 transition-colors"
                            active-class="text-blue-600 font-semibold"
                        >
                            {{ $t('expenses') }}
                        </router-link>
                        <router-link
                            v-if="isAdmin"
                            to="/admin/incomes"
                            class="hover:text-gray-900 transition-colors"
                            active-class="text-blue-600 font-semibold"
                        >
                            {{ $t('incomes') }}
                        </router-link>
                        <router-link
                            v-if="isAdmin"
                            to="/admin/users"
                            class="hover:text-gray-900 transition-colors"
                            active-class="text-blue-600 font-semibold"
                        >
                            {{ $t('users') }}
                        </router-link>
                        <router-link
                            v-if="isAdmin && (user?.role === 'super_admin')"
                            to="/admin/buildings"
                            class="hover:text-gray-900 transition-colors"
                            active-class="text-blue-600 font-semibold"
                        >
                            {{ $t('buildings') }}
                        </router-link>
                        <router-link
                            v-if="isAdmin && (user?.role !== 'super_admin')"
                            to="/admin/my-building"
                            class="hover:text-gray-900 transition-colors"
                            active-class="text-blue-600 font-semibold"
                        >
                            {{ $t('my_building') }}
                        </router-link>

                        <!-- Neighbor Navigation -->
                        <router-link
                            v-if="isNeighbor"
                            to="/neighbor"
                            class="hover:text-gray-900 transition-colors"
                            active-class="text-blue-600 font-semibold"
                        >
                            {{ $t('neighbor_dashboard') }}
                        </router-link>
                        <router-link
                            v-if="isNeighbor"
                            to="/neighbor/profile"
                            class="hover:text-gray-900 transition-colors"
                            active-class="text-blue-600 font-semibold"
                        >
                            {{ $t('my_profile') }}
                        </router-link>
                    </div>

                    <!-- Right - Language Toggle, User Info and Logout -->
                    <div class="flex items-center space-x-4" :class="$isRTL() ? 'space-x-reverse' : ''">
                        <language-toggle />
                        <span class="text-gray-600 font-medium">{{ user?.name }}</span>
                        <button
                            @click="logout"
                            class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
                        >
                            {{ $t('logout') }}
                        </button>
                    </div>
                </div>

                <!-- Mobile Navigation Menu -->
                <div class="md:hidden mt-4 pt-4 border-t border-gray-200">
                    <div class="flex flex-col space-y-2">
                        <!-- Admin Mobile Menu -->
                        <template v-if="isAdmin">
                            <router-link to="/admin" class="block py-2 text-gray-600 hover:text-gray-900 transition-colors" active-class="text-blue-600 font-semibold">{{ $t('admin_dashboard') }}</router-link>
                            <router-link to="/admin/expenses" class="block py-2 text-gray-600 hover:text-gray-900 transition-colors" active-class="text-blue-600 font-semibold">{{ $t('expenses') }}</router-link>
                            <router-link to="/admin/incomes" class="block py-2 text-gray-600 hover:text-gray-900 transition-colors" active-class="text-blue-600 font-semibold">{{ $t('incomes') }}</router-link>
                            <router-link to="/admin/users" class="block py-2 text-gray-600 hover:text-gray-900 transition-colors" active-class="text-blue-600 font-semibold">{{ $t('users') }}</router-link>
                            <router-link
                                v-if="user?.role === 'super_admin'"
                                to="/admin/buildings"
                                class="block py-2 text-gray-600 hover:text-gray-900 transition-colors"
                                active-class="text-blue-600 font-semibold"
                            >
                                {{ $t('buildings') }}
                            </router-link>
                            <router-link
                                v-if="user?.role !== 'super_admin'"
                                to="/admin/my-building"
                                class="block py-2 text-gray-600 hover:text-gray-900 transition-colors"
                                active-class="text-blue-600 font-semibold"
                            >
                                {{ $t('my_building') }}
                            </router-link>
                        </template>

                        <!-- Neighbor Mobile Menu -->
                        <template v-if="isNeighbor">
                            <router-link to="/neighbor" class="block py-2 text-gray-600 hover:text-gray-900 transition-colors" active-class="text-blue-600 font-semibold">{{ $t('neighbor_dashboard') }}</router-link>
                            <router-link to="/neighbor/profile" class="block py-2 text-gray-600 hover:text-gray-900 transition-colors" active-class="text-blue-600 font-semibold">{{ $t('my_profile') }}</router-link>
                        </template>

                        <!-- User Info and Logout -->
                        <div class="border-t border-gray-200 mt-2 pt-2">
                            <div class="py-2 text-sm text-gray-500">{{ user?.name }}</div>
                            <button
                                @click="logout"
                                class="block w-full text-left py-2 text-red-600 hover:text-red-800 transition-colors font-medium"
                            >
                                {{ $t('logout') }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Guest Navigation for all guest pages -->
        <nav v-if="!isAuthenticated && isGuestPage" :key="currentLocale" class="w-full bg-white shadow-lg">
            <div class="max-w-6xl mx-auto px-8 py-4">
                <div class="flex justify-between items-center">
                    <!-- Left - App Name -->
                    <router-link to="/" class="text-xl lg:text-2xl font-bold text-blue-900 hover:text-blue-700 transition-colors">
                        {{ $t('app_name') }}
                    </router-link>

                    <!-- Center - Navigation Menu (Desktop) -->
                    <div class="hidden md:flex space-x-8 text-lg font-medium text-gray-600">
                        <router-link to="/" class="hover:text-gray-900 transition-colors" active-class="text-blue-600 font-semibold">{{ $t('home') }}</router-link>
                        <router-link to="/about" class="hover:text-gray-900 transition-colors" active-class="text-blue-600 font-semibold">{{ $t('about') }}</router-link>
                        <router-link to="/services" class="hover:text-gray-900 transition-colors" active-class="text-blue-600 font-semibold">{{ $t('services') }}</router-link>
                        <router-link to="/contact" class="hover:text-gray-900 transition-colors" active-class="text-blue-600 font-semibold">{{ $t('contact') }}</router-link>
                    </div>

                    <!-- Right - Language Toggle and Login -->
                    <div class="flex items-center space-x-4" :class="$isRTL() ? 'space-x-reverse' : ''">
                        <language-toggle />
                        <router-link to="/login" class="bg-blue-900 hover:bg-blue-800 text-white px-4 py-2 rounded-lg transition-colors">
                            {{ $t('login') }}
                        </router-link>
                    </div>
                </div>

                <!-- Mobile Navigation Menu -->
                <div class="md:hidden mt-4 pt-4 border-t border-gray-200">
                    <div class="flex flex-col space-y-2">
                        <router-link to="/" class="block py-2 text-gray-600 hover:text-gray-900 transition-colors" active-class="text-blue-600 font-semibold">{{ $t('home') }}</router-link>
                        <router-link to="/about" class="block py-2 text-gray-600 hover:text-gray-900 transition-colors" active-class="text-blue-600 font-semibold">{{ $t('about') }}</router-link>
                        <router-link to="/services" class="block py-2 text-gray-600 hover:text-gray-900 transition-colors" active-class="text-blue-600 font-semibold">{{ $t('services') }}</router-link>
                        <router-link to="/contact" class="block py-2 text-gray-600 hover:text-gray-900 transition-colors" active-class="text-blue-600 font-semibold">{{ $t('contact') }}</router-link>
                        <router-link to="/login" class="block py-2 text-blue-600 hover:text-blue-800 transition-colors font-medium">{{ $t('login') }}</router-link>
                    </div>
                </div>
            </div>
        </nav>

        <main :class="isGuestPage ? '' : 'max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8'">
            <router-view />
        </main>
    </div>
</template>

<script>
import LanguageToggle from './LanguageToggle.vue';
import i18nMixin from '../mixins/i18nMixin.js';

export default {
    name: 'App',
    mixins: [i18nMixin],
    components: {
        LanguageToggle
    },
    data() {
        return {
            user: null,
            mobileMenuOpen: false,
            localeUpdateKey: 0
        };
    },
    computed: {
        isAuthenticated() {
            return !!localStorage.getItem('token') && !!this.user;
        },
        isAdmin() {
            return this.user?.role === 'admin' || this.user?.role === 'super_admin';
        },
        isNeighbor() {
            return this.user?.role === 'neighbor';
        },
        isHomePage() {
            return this.$route.name === 'Home';
        },
        isGuestPage() {
            return ['Home', 'About', 'Services', 'Contact'].includes(this.$route.name);
        },
        // Force reactivity for language changes
        currentLocale() {
            return this.$locale() + this.localeUpdateKey;
        }
    },
    watch: {
        '$route'() {
            // Update user data when route changes
            this.user = JSON.parse(localStorage.getItem('user') || 'null');
        }
    },
    created() {
        this.user = JSON.parse(localStorage.getItem('user') || 'null');
        // Listen for storage changes to update authentication state
        window.addEventListener('storage', this.handleStorageChange);
        // Also listen for custom events when localStorage is updated in the same tab
        window.addEventListener('auth-state-changed', this.handleAuthStateChange);
        // Listen for language changes
        window.addEventListener('localeChanged', this.handleLocaleChange);
        window.addEventListener('forceUpdate', this.handleForceUpdate);
    },
    mounted() {
        // Force update user data on mount
        this.user = JSON.parse(localStorage.getItem('user') || 'null');
        this.$forceUpdate();
    },
    beforeUnmount() {
        window.removeEventListener('storage', this.handleStorageChange);
        window.removeEventListener('auth-state-changed', this.handleAuthStateChange);
        window.removeEventListener('localeChanged', this.handleLocaleChange);
        window.removeEventListener('forceUpdate', this.handleForceUpdate);
    },
    methods: {
        handleStorageChange(event) {
            if (event.key === 'user' || event.key === 'token') {
                this.user = JSON.parse(localStorage.getItem('user') || 'null');
            }
        },
        handleAuthStateChange() {
            this.user = JSON.parse(localStorage.getItem('user') || 'null');
            this.$forceUpdate();
        },
        handleLocaleChange() {
            this.localeUpdateKey++;
            this.$forceUpdate();
        },
        handleForceUpdate() {
            this.localeUpdateKey++;
            this.$forceUpdate();
        },
        async logout() {
            try {
                await this.$axios.post('/logout');
            } catch (error) {
                console.error('Logout error:', error);
            } finally {
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                this.user = null;
                // Dispatch custom event to notify other components
                window.dispatchEvent(new CustomEvent('auth-state-changed'));
                this.$router.push('/login');
            }
        }
    }
}
</script> 